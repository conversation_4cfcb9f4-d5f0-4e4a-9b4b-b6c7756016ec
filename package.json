{"name": "try<PERSON>", "version": "0.7.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/mongoose": "^5.11.97", "bcryptjs": "^3.0.2", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "next": "15.2.4", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-spinners": "^0.15.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^3.0.0", "@types/node": "^20.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}}