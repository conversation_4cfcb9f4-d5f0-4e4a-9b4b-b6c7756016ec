
"use client"

import { useState, useEffect } from 'react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, arrayMove, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { Board, Column, Task } from '@/types';
import ColumnContainer from './ColumnContainer';
import AddColumnButton from './AddColumnButton';
import LoadingSpinner from '../common/LoadingSpinner'; // Import your LoadingSpinner component

interface KanbanBoardProps {
  board: Board;
  initialColumns: Column[];
  initialTasks: Task[];
  saveDisplayState?: boolean;
  userId: string;
}

export default function KanbanBoard({ 
  board, 
  initialColumns, 
  initialTasks,
  saveDisplayState = false,
  userId
}: KanbanBoardProps) {
  const [columns, setColumns] = useState<Column[]>(initialColumns);
  const [tasks, setTasks] = useState<Task[]>(initialTasks);
  const [activeColumn, setActiveColumn] = useState<Column | null>(null);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState<boolean>(false); // State to manage loading

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // 3px
      },
    })
  );

  // Handle adding a new column
  // Memoize handlers to prevent unnecessary re-renders
  const handleColumnAdded = useCallback((newColumn: Column) => {
    setColumns(prev => [...prev, newColumn]);
  }, []);

  const handleTaskAdded = useCallback((newTask: Task) => {
    setTasks(prev => [...prev, newTask]);
  }, []);

  // Handle column deletion
  const handleColumnDeleted = async (columnId: string) => {
    setLoading(true); // Add loading state
    try {
      const response = await fetch(`/api/columns/${columnId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setColumns(prev => prev.filter(col => col._id !== columnId));
        setTasks(prev => prev.filter(task => task.columnId !== columnId));
      } else {
        console.error('Failed to delete column:', await response.text());
      }
    } catch (error) {
      console.error('Failed to delete column:', error);
    } finally {
      setLoading(false); // End loading state
    }
  };

  // Handle task deletion
  const handleTaskDeleted = async (taskId: string) => {
    setLoading(true); // Start loading
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setTasks(prev => prev.filter(task => task._id !== taskId));
      } else {
        console.error('Failed to delete task:', await response.text());
      }
    } catch (error) {
      console.error('Failed to delete task:', error);
    } finally {
      setLoading(false); // Stop loading
    }
  };

  // Handle column update
  const handleColumnUpdated = (updatedColumn: Column) => {
    setColumns(prev => 
      prev.map(col => col._id === updatedColumn._id ? updatedColumn : col)
    );
  };

  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
  };

  const closeTaskDetailsModal = () => {
    setSelectedTask(null);
  };

  // Add function to handle task priority updates
  const handleTaskPriorityUpdated = async (taskId: string, priority: string) => {
    setLoading(true); // Start loading
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ priority }),
      });
      
      if (response.ok) {
        const data = await response.json();
        const updatedTask = data.data;
        
        // Update the task in the local state
        setTasks(prev => 
          prev.map(task => task._id === taskId ? updatedTask : task)
        );
      } else {
        console.error('Failed to update task priority:', await response.text());
      }
    } catch (error) {
      console.error('Failed to update task priority:', error);
    } finally {
      setLoading(false); // Stop loading
    }
  };

  // Add the missing drag event handlers
  const onDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const activeId = active.id.toString();

    // Check if we're dragging a column
    const draggedColumn = columns.find(col => col._id === activeId);
    if (draggedColumn) {
      setActiveColumn(draggedColumn);
      return;
    }

    // Check if we're dragging a task
    const draggedTask = tasks.find(task => task._id === activeId);
    if (draggedTask) {
      setActiveTask(draggedTask);
    }
  };

  const onDragEnd = async (event: DragEndEvent) => {
    setActiveColumn(null);
    setActiveTask(null);

    const { active, over } = event;
    
    if (!over) return;
    
    const activeId = active.id.toString();
    const overId = over.id.toString();
    
    if (activeId === overId) return;

    // Find the indexes of the active and over columns
    const activeColumnIndex = columns.findIndex(col => col._id === activeId);
    
    // If we're not dragging a column, return
    if (activeColumnIndex < 0) return;
    
    const overColumnIndex = columns.findIndex(col => col._id === overId);
    
    // Update the columns array with the new order
    const updatedColumns = arrayMove(columns, activeColumnIndex, overColumnIndex);
    
    // Update the position property of each column
    const columnsWithUpdatedPositions = updatedColumns.map((col, index) => ({
      ...col,
      position: index
    }));
    
    setColumns(columnsWithUpdatedPositions);
    
    // Update the positions in the database
    try {
      await Promise.all(columnsWithUpdatedPositions.map(column => 
        fetch(`/api/columns/${column._id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ position: column.position }),
        })
      ));
    } catch (error) {
      console.error('Failed to update column positions:', error);
    }
  };

  const onDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over) return;
    
    const activeId = active.id.toString();
    const overId = over.id.toString();
    
    if (activeId === overId) return;

    // Check if we're dragging a task
    const activeTask = tasks.find(task => task._id === activeId);
    if (!activeTask) return;
    
    // Find the destination column
    const overColumn = columns.find(col => col._id === overId);
    
    // If we're dragging over a column, move the task to that column
    if (overColumn) {
      setTasks(prev => {
        const updatedTasks = prev.map(task => {
          if (task._id === activeId) {
            return { ...task, columnId: overId };
          }
          return task;
        });
        
        // Update the task in the database
        fetch(`/api/tasks/${activeId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ columnId: overId }),
        }).catch(error => {
          console.error('Failed to update task column:', error);
        });
        
        return updatedTasks;
      });
    }
  };

  // Add effect to load saved display state
  useEffect(() => {
    if (saveDisplayState && userId) {
      // Load saved display state from localStorage
      const savedState = localStorage.getItem(`board-display-${board._id}-${userId}`);
      if (savedState) {
        try {
          const parsedState = JSON.parse(savedState);
          if (parsedState.columns) setColumns(parsedState.columns);
          // Only update tasks if they match the current tasks (by ID)
          if (parsedState.tasks && parsedState.tasks.length === tasks.length) {
            // Create a map of task IDs to check if all tasks match
            const taskIds = new Set(tasks.map(t => t._id));
            const savedTaskIds = new Set(parsedState.tasks.map(t => t._id));
            
            // Check if all current tasks exist in saved tasks
            const allTasksMatch = [...taskIds].every(id => savedTaskIds.has(id));
            
            if (allTasksMatch) {
              setTasks(parsedState.tasks);
            }
          }
        } catch (error) {
          console.error('Error loading saved board state:', error);
        }
      }
    }
  }, [board._id, userId, saveDisplayState]);
  
  // Add effect to save display state when it changes
  useEffect(() => {
    if (saveDisplayState && userId) {
      // Save current display state to localStorage
      const displayState = {
        columns,
        tasks,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem(`board-display-${board._id}-${userId}`, JSON.stringify(displayState));
    }
  }, [columns, tasks, board._id, userId, saveDisplayState]);
  
  return (
    <DndContext 
      sensors={sensors}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onDragOver={onDragOver}
    >
      {loading && <LoadingSpinner />} {/* Display loading spinner when loading */}
      <div className="flex gap-4 overflow-x-auto pb-4 pt-2 px-2 min-h-[calc(100vh-200px)]">
        <SortableContext items={columns.map(col => col._id)} strategy={horizontalListSortingStrategy}>
          {columns.map(column => (
            <ColumnContainer 
              key={column._id} 
              column={column} 
              tasks={tasks.filter(task => task.columnId === column._id)}
              onTaskAdded={handleTaskAdded}
              onColumnDeleted={handleColumnDeleted}
              onTaskDeleted={handleTaskDeleted}
              onColumnUpdated={handleColumnUpdated}
              onUpdateTaskPriority={handleTaskPriorityUpdated} // Pass the handler to ColumnContainer
              onTaskClick={handleTaskClick}
            />
          ))}
        </SortableContext>
        
        <AddColumnButton 
          boardId={board._id} 
          onColumnAdded={handleColumnAdded} 
        />
      </div>
      
      {/* Add task details modal if needed */}
      {selectedTask && (
        <div className="modal">
          {/* Task details modal content */}
          <button onClick={closeTaskDetailsModal}>Close</button>
        </div>
      )}
    </DndContext>
  );
}
