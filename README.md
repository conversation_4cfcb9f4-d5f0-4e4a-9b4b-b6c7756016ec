# Tryllo - Kanban Board Application

Tryllo is a modern Kanban board application built with Next.js, MongoDB, and dnd-kit for drag-and-drop functionality. It allows you to manage tasks across customizable columns with a clean, intuitive interface.

## Features

- **Drag and Drop Interface**: Easily move tasks between columns
- **Customizable Columns**: Create, rename, and reorder columns
- **Task Management**: Create, edit, and delete tasks
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- **Frontend**: Next.js, React, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Mongoose
- **Drag and Drop**: dnd-kit library
- **Styling**: Tailwind CSS

## Getting Started

## Usage
- Create columns and tasks
- Drag to reorder or move between columns
- Edit task details with a click

### Installation

1. Clone the repository:

```bash
git clone https://github.com/maro14/tryllo.git
cd tryllo
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env.local` file in the root directory with the following variables:

```
MONGODB_URI=your_mongodb_connection_string
NEXT_PUBLIC_API_URL=http://localhost:3000
```

4. Run the development server:

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Usage

- **Create a new column**: Click the "Add Column" button
- **Add a task**: Click the "Add Task" button in a column
- **Move a task**: Drag and drop tasks between columns
- **Reorder columns**: Drag and drop columns to reorder them

```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
