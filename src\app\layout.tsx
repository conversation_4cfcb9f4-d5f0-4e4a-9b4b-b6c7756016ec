import { Inter } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from '@/components/theme-provider';
import Navbar from '@/components/layout/NavBar';
import Footer from '@/components/layout/Footer';
import { AuthProvider } from '@/components/auth-provider';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Tryllo - Kanban Board',
  description: 'A simple Kanban board application built with Next.js',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body 
        className="inter_59dee874-module__9CtR0q__className min-h-screen flex flex-col bg-gray-50 da..." 
        suppressHydrationWarning={true}
      >
        {children}
      </body>
    </html>
  );
}
