"use client";

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Board } from '@/types';
import LoadingSpinner from '@/components/common/LoadingSpinner';

interface Board {
  _id: string;
  title: string;
  description?: string;
  createdAt: string | Date;
  updatedAt: string | Date;
}

interface BoardListProps {
  userId: string;
}

export default function BoardList({ userId }: BoardListProps) {
  const [boards, setBoards] = useState<Board[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchBoards = async () => {
      try {
        const response = await fetch(`/api/boards?userId=${userId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch boards');
        }
        
        const data = await response.json();
        setBoards(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching boards:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBoards();
  }, [userId]);

  if (isLoading) {
    return (
      <div className="col-span-2 flex justify-center items-center h-40">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="col-span-2 bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
        <p className="text-red-600 dark:text-red-400">Error loading boards: {error}</p>
      </div>
    );
  }

  if (boards.length === 0) {
    return (
      <div className="col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
        <p className="text-gray-600 dark:text-gray-400">
          You haven't created any custom boards yet.
        </p>
      </div>
    );
  }

  return (
    <>
      {boards.map((board) => (
        <div 
          key={board._id} 
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700"
        >
          <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">{board.title}</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {board.description || "No description provided"}
          </p>
          <div className="flex justify-between items-center">
            <Link 
              href={`/board/${board._id}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              View Board
            </Link>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Created: {new Date(board.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>
      ))}
    </>
  );
}