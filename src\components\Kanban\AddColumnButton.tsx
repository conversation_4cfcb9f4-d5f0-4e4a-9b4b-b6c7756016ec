"use client"

import { useState, KeyboardEvent } from 'react';
import { FiPlus, FiCheck, FiX } from 'react-icons/fi';
import { Column } from '@/types';

interface AddColumnButtonProps {
  boardId: string;
  onColumnAdded?: (column: Column) => void;
}

export default function AddColumnButton({ boardId, onColumnAdded }: AddColumnButtonProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [columnTitle, setColumnTitle] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAddColumn = async () => {
    if (!columnTitle.trim() || isLoading) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/columns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: columnTitle,
          boardId: boardId,
        }),
      });
      
      if (response.ok) {
        const newColumn = await response.json();
        if (onColumnAdded) {
          onColumnAdded(newColumn);
        }
        setColumnTitle('');
        setIsAdding(false);
      } else {
        const errorText = await response.text();
        console.error('Failed to add column:', errorText);
        setError('Failed to add column. Please try again.');
      }
    } catch (error) {
      console.error('Failed to add column:', error);
      setError('Network error. Please check your connection.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleAddColumn();
    } else if (e.key === 'Escape') {
      setIsAdding(false);
    }
  };

  if (isAdding) {
    return (
      <div className="glass-effect p-4 rounded-lg column-shadow min-w-[350px] w-[350px] flex-shrink-0 h-fit">
        <h3 className="font-semibold mb-4">Add New Column</h3>
        <input
          type="text"
          className="w-full p-3 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-lg mb-3 text-foreground text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="Enter column title..."
          value={columnTitle}
          onChange={(e) => setColumnTitle(e.target.value)}
          onKeyDown={handleKeyDown}
          autoFocus
          maxLength={50}
        />
        {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
        <div className="flex gap-2">
          <button
            className="bg-primary hover:bg-primary-hover text-white px-3 py-2 rounded-lg text-sm font-medium flex items-center gap-1 transition-colors disabled:opacity-50"
            onClick={handleAddColumn}
            disabled={isLoading || !columnTitle.trim()}
          >
            {isLoading ? 'Adding...' : <><FiCheck size={14} /> Add</>}
          </button>
          <button
            className="border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 px-3 py-2 rounded-lg text-sm font-medium flex items-center gap-1 transition-colors"
            onClick={() => setIsAdding(false)}
            disabled={isLoading}
          >
            <FiX size={14} /> Cancel
          </button>
        </div>
      </div>
    );
  }

  return (
    <button
      className="glass-effect p-4 rounded-lg column-shadow min-w-[350px] w-[350px] flex-shrink-0 h-[150px] flex flex-col items-center justify-center gap-2 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-colors"
      onClick={() => setIsAdding(true)}
      aria-label="Add new column"
      aria-haspopup="dialog"
    >
      <FiPlus size={24} className="text-gray-500" aria-hidden="true" />
      <span className="text-gray-500 font-medium">Add Column</span>
    </button>
  );
}