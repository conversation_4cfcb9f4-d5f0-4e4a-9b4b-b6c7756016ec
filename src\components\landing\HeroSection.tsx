import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/fi';
import Image from 'next/image';

export default function HeroSection() {
  return (
    <div className="container mx-auto px-4 pt-24 pb-20">
      <div className="flex flex-col items-center text-center">
        <div className="flex items-center mb-8 animate-fade-in">
          <FiTrello className="h-12 w-12 text-blue-600 dark:text-blue-400 mr-3" />
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white">Tryllo</h1>
        </div>
        
        <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold text-gray-800 dark:text-white mb-8 max-w-4xl leading-tight animate-fade-in-up">
          Organize your tasks with a <span className="text-blue-600 dark:text-blue-400 relative">
            simple, flexible
            <span className="absolute bottom-0 left-0 w-full h-1 bg-blue-600 dark:bg-blue-400 rounded-full"></span>
          </span> Kanban board
        </h2>
        
        <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl animate-fade-in-up animation-delay-200">
          Boost your productivity with a visual task management tool that adapts to your workflow.
        </p>
        
        <div className="flex flex-wrap justify-center gap-4 mb-12 animate-fade-in-up animation-delay-250">
          <div className="flex items-center">
            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 mr-2">
              <FiCheck className="text-blue-600 dark:text-blue-400" size={14} />
            </span>
            <span className="text-gray-700 dark:text-gray-300">Drag & Drop Interface</span>
          </div>
          <div className="flex items-center">
            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 mr-2">
              <FiCheck className="text-blue-600 dark:text-blue-400" size={14} />
            </span>
            <span className="text-gray-700 dark:text-gray-300">Real-time Updates</span>
          </div>
          <div className="flex items-center">
            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 mr-2">
              <FiCheck className="text-blue-600 dark:text-blue-400" size={14} />
            </span>
            <span className="text-gray-700 dark:text-gray-300">Dark Mode Support</span>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-5 animate-fade-in-up animation-delay-300">
          <Link 
            href="/signup" 
            className="group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium text-lg shadow-md hover:shadow-lg transition-all flex items-center justify-center"
          >
            Get Started
            <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>
          <Link 
            href="/login" 
            className="bg-white dark:bg-gray-800 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 px-8 py-4 rounded-lg font-medium text-lg shadow-sm hover:shadow-md transition-all"
          >
            Sign In
          </Link>
        </div>
        
        <div className="mt-16 relative w-full max-w-5xl animate-fade-in-up animation-delay-400">
          <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur opacity-30 dark:opacity-40 animate-pulse"></div>
          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
            <div className="p-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 flex items-center">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="ml-4 text-xs text-gray-500 dark:text-gray-400">Tryllo - Kanban Board</div>
            </div>
            <div className="p-4">
              <Image 
                src="/board-preview.png" 
                alt="Tryllo board preview" 
                width={1200} 
                height={675}
                className="rounded shadow-sm hover:shadow-md transition-shadow"
                priority
              />
            </div>
          </div>
          <div className="absolute -bottom-4 -right-4 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg transform rotate-3 animate-bounce-slow">
            Try it now!
          </div>
        </div>
      </div>
    </div>
  );
}